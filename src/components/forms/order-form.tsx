'use client'

import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Combobox } from '@/components/ui/combobox'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { LuUpload, LuPlus, LuMinus, LuTrash2, LuTriangleAlert, LuLoader } from 'react-icons/lu'
import { useAppStore } from '@/lib/store'
import { ImagePreview } from '@/components/ui/image-preview'
import { getUsageUnitOptions } from '@/lib/usage-units'
import { PricingCalculator } from '@/components/pricing/pricing-calculator'

interface Order {
  id: number
  productName: string
  quantity: number
  usageUnit?: string | null
  comment?: string | null
  imageFilename: string | null
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  isBought: boolean
  packingStatus: string
  storeCodeId: number | null
  customerId: number | null
  storeCode?: {
    id: number
    code: string
    name: string
  }
  customer?: {
    id: number
    name: string
  }
  createdAt: string
  updatedAt: string
}

const orderFormSchema = z.object({
  productName: z.string()
    .min(1, 'Product name is required')
    .max(255, 'Product name must be less than 255 characters')
    .trim(),
  quantity: z.number()
    .min(1, 'Quantity must be at least 1')
    .max(9999, 'Quantity cannot exceed 9999')
    .int('Quantity must be a whole number'),
  usageUnit: z.string().optional(),
  comment: z.string()
    .max(500, 'Comment must be less than 500 characters')
    .optional(),
  storePrice: z.number()
    .min(0, 'Store price cannot be negative')
    .max(999999.99, 'Store price cannot exceed ₱999,999.99'),
  pasabuyFee: z.number()
    .min(0, 'Pasabuy fee cannot be negative')
    .max(999999.99, 'Pasabuy fee cannot exceed ₱999,999.99'),
  customerPrice: z.number()
    .min(0, 'Customer price cannot be negative')
    .max(999999.99, 'Customer price cannot exceed ₱999,999.99'),
  storeCodeId: z.string().optional(),
  customerId: z.string().optional(),
})

type OrderFormData = z.infer<typeof orderFormSchema>

interface OrderFormProps {
  onSubmit: (data: OrderFormData & { imageFile?: File }) => Promise<void>
  onCancel: () => void
  onDelete?: (orderId: number) => Promise<void>
  initialData?: Partial<OrderFormData>
  existingImageFilename?: string | null
  isLoading?: boolean
  orderId?: number
  isEditing?: boolean
}

export function OrderForm({
  onSubmit,
  onCancel,
  onDelete,
  initialData,
  existingImageFilename,
  isLoading = false,
  orderId,
  isEditing = false
}: OrderFormProps) {
  const { storeCodes, customers, orders, setStoreCodes, setCustomers, setOrders, addStoreCode, addCustomer } = useAppStore()
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [isCreatingStoreCode, setIsCreatingStoreCode] = useState(false)
  const [isCreatingCustomer, setIsCreatingCustomer] = useState(false)
  const [usageUnits, setUsageUnits] = useState<string[]>([])
  const [isCreatingUsageUnit, setIsCreatingUsageUnit] = useState(false)

  // Form submission states
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [submitSuccess, setSubmitSuccess] = useState(false)

  // Delete confirmation dialog state
  const [deleteDialog, setDeleteDialog] = useState({
    isOpen: false,
    isLoading: false,
    hasInvoiceItems: false
  })

  const form = useForm<OrderFormData>({
    resolver: zodResolver(orderFormSchema),
    defaultValues: {
      productName: '',
      quantity: 1,
      usageUnit: '',
      comment: '',
      storePrice: 0,
      pasabuyFee: 0,
      customerPrice: 0,
      storeCodeId: '',
      customerId: '',
      ...initialData,
    },
  })

  const { watch, setValue } = form
  const storePrice = watch('storePrice')
  const pasabuyFee = watch('pasabuyFee')
  const quantity = watch('quantity')

  // Set existing image preview when editing
  useEffect(() => {
    if (existingImageFilename && !imagePreview && !imageFile) {
      setImagePreview(`/api/images/orders/${existingImageFilename}`)
    }
  }, [existingImageFilename, imagePreview, imageFile])

  // Update form values when initialData changes (for pre-population)
  useEffect(() => {
    if (initialData) {
      Object.entries(initialData).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          form.setValue(key as keyof OrderFormData, value)
        }
      })
    }
  }, [initialData, form])

  // Remove auto-calculation of reseller price - it should be manually entered

  // Load store codes, customers, orders, and usage units for autocomplete
  useEffect(() => {
    async function loadData() {
      try {
        const [storeCodesRes, customersRes, ordersRes] = await Promise.all([
          fetch('/api/store-codes'),
          fetch('/api/customers'),
          fetch('/api/orders')
        ])

        if (storeCodesRes.ok) {
          const storeCodesData = await storeCodesRes.json()
          setStoreCodes(storeCodesData)
        }

        if (customersRes.ok) {
          const customersData = await customersRes.json()
          setCustomers(customersData)
        }

        if (ordersRes.ok) {
          const ordersData = await ordersRes.json()

          // Handle both legacy and advanced response formats
          let ordersArray: Order[]
          if (Array.isArray(ordersData)) {
            // Legacy format - direct array
            ordersArray = ordersData
          } else if (ordersData && ordersData.data && Array.isArray(ordersData.data)) {
            // Advanced format - structured response
            ordersArray = ordersData.data
          } else {
            ordersArray = []
          }

          setOrders(ordersArray)

          // Extract unique usage units from existing orders
          const existingUsageUnits = [...new Set(
            ordersArray
              .map((order: Order) => order.usageUnit)
              .filter((unit: string | null | undefined): unit is string =>
                unit != null && unit.trim() !== ''
              )
          )]

          // Combine predefined units with existing units
          const predefinedUnits = getUsageUnitOptions().map(option => option.value)
          const allUnits = [...new Set([...predefinedUnits, ...existingUsageUnits])]
          setUsageUnits(allUnits)
        }
      } catch (error) {
        console.error('Error loading form data:', error)
      }
    }

    if (storeCodes.length === 0 || customers.length === 0 || orders.length === 0 || usageUnits.length === 0) {
      loadData()
    }
  }, [storeCodes.length, customers.length, orders.length, usageUnits.length, setStoreCodes, setCustomers, setOrders])

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setImageFile(file)
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const removeImage = () => {
    setImageFile(null)
    setImagePreview(null)
    // Clear the file input
    const fileInput = document.getElementById('image-upload') as HTMLInputElement
    if (fileInput) {
      fileInput.value = ''
    }
  }

  const triggerImageUpload = () => {
    const fileInput = document.getElementById('image-upload') as HTMLInputElement
    if (fileInput) {
      fileInput.click()
    }
  }

  const adjustQuantity = (delta: number) => {
    const currentQuantity = form.getValues('quantity')
    const newQuantity = Math.max(1, currentQuantity + delta)
    setValue('quantity', newQuantity)
  }

  const adjustPrice = (field: 'storePrice' | 'pasabuyFee' | 'customerPrice', delta: number) => {
    const currentValue = form.getValues(field)
    const newValue = Math.max(0, currentValue + delta)
    setValue(field, newValue)
  }

  const handleSubmit = async (data: OrderFormData) => {
    try {
      setIsSubmitting(true)
      setSubmitError(null)
      setSubmitSuccess(false)

      // Convert empty strings to undefined for optional fields
      const processedData = {
        ...data,
        usageUnit: data.usageUnit?.trim() || undefined,
        comment: data.comment?.trim() || undefined,
        storeCodeId: data.storeCodeId?.trim() || undefined,
        customerId: data.customerId?.trim() || undefined,
        imageFile: imageFile || undefined
      }

      await onSubmit(processedData)
      setSubmitSuccess(true)
    } catch (error) {
      console.error('Form submission error:', error)
      setSubmitError(error instanceof Error ? error.message : 'An unexpected error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCreateStoreCode = async (searchTerm: string) => {
    try {
      setIsCreatingStoreCode(true)
      const response = await fetch('/api/store-codes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: searchTerm.toUpperCase(),
          name: searchTerm
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create store code')
      }

      const newStoreCode = await response.json()
      addStoreCode(newStoreCode)
      form.setValue('storeCodeId', newStoreCode.id.toString())
    } catch (error) {
      console.error('Error creating store code:', error)
      // You might want to show a toast notification here
    } finally {
      setIsCreatingStoreCode(false)
    }
  }

  const handleCreateCustomer = async (searchTerm: string) => {
    try {
      setIsCreatingCustomer(true)
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: searchTerm.trim() }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create customer')
      }

      const newCustomer = await response.json()
      addCustomer(newCustomer)
      form.setValue('customerId', newCustomer.id.toString())
    } catch (error) {
      console.error('Error creating customer:', error)
      // You might want to show a toast notification here
    } finally {
      setIsCreatingCustomer(false)
    }
  }

  const handleCreateUsageUnit = async (searchTerm: string) => {
    try {
      setIsCreatingUsageUnit(true)
      // For usage units, we just add them to the local state since they're simple strings
      const newUnit = searchTerm.trim()
      if (newUnit && !usageUnits.includes(newUnit)) {
        setUsageUnits(prev => [...prev, newUnit])
        form.setValue('usageUnit', newUnit)
      }
    } catch (error) {
      console.error('Error creating usage unit:', error)
    } finally {
      setIsCreatingUsageUnit(false)
    }
  }

  // Delete functionality
  const handleDeleteClick = async () => {
    if (!orderId) return

    try {
      // Check if order has invoice items
      const response = await fetch(`/api/orders/${orderId}`)
      if (response.ok) {
        const order = await response.json()
        setDeleteDialog({
          isOpen: true,
          isLoading: false,
          hasInvoiceItems: order.invoiceItems && order.invoiceItems.length > 0
        })
      } else {
        // If we can't fetch the order, still show the dialog but without invoice info
        setDeleteDialog({
          isOpen: true,
          isLoading: false,
          hasInvoiceItems: false
        })
      }
    } catch (error) {
      console.error('Error checking order details:', error)
      // Show dialog anyway
      setDeleteDialog({
        isOpen: true,
        isLoading: false,
        hasInvoiceItems: false
      })
    }
  }

  const handleDeleteConfirm = async () => {
    if (!orderId || !onDelete) return

    try {
      setDeleteDialog(prev => ({ ...prev, isLoading: true }))
      await onDelete(orderId)
      setDeleteDialog({ isOpen: false, isLoading: false, hasInvoiceItems: false })
    } catch (error) {
      console.error('Error deleting order:', error)
      setDeleteDialog(prev => ({ ...prev, isLoading: false }))
      // Error handling will be done by the parent component
    }
  }

  const handleDeleteCancel = () => {
    setDeleteDialog({ isOpen: false, isLoading: false, hasInvoiceItems: false })
  }

  const orderProfit = quantity * pasabuyFee
  const orderTotalCustomerPrice = quantity * form.watch('customerPrice')
  const orderTotalStoreCost = quantity * storePrice

  // Prepare options for comboboxes
  const storeCodeOptions = storeCodes.map(storeCode => ({
    value: storeCode.id.toString(),
    label: `${storeCode.name || storeCode.code} (${storeCode.code})`
  }))

  const customerOptions = customers.map(customer => ({
    value: customer.id.toString(),
    label: customer.name
  }))

  // Get unique product names from existing orders, sorted alphabetically
  const productNameOptions = React.useMemo(() => {
    if (!Array.isArray(orders)) return []

    const uniqueNames = Array.from(new Set(orders.map(order => order.productName)))
      .filter(name => name && name.trim().length > 0)
      .sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()))

    return uniqueNames.map(name => ({
      value: name,
      label: name
    }))
  }, [orders])

  const usageUnitOptions = usageUnits.map(unit => ({
    value: unit,
    label: unit
  }))

  return (

    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-3">
               {/* Store and Customer */}
        <Card className="p-3">
          <h3 className="text-base font-medium mb-3">Store & Customer</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <FormField
              control={form.control}
              name="customerId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">Customer</FormLabel>
                  <FormControl>
                    <Combobox
                      options={customerOptions}
                      value={field.value || ""}
                      onValueChange={field.onChange}
                      placeholder="Select customer..."
                      searchPlaceholder="Search customers..."
                      emptyText="No customer found."
                      onCreateNew={handleCreateCustomer}
                      isCreating={isCreatingCustomer}
                      className="h-9 text-base"
                    />
                  </FormControl>
                  <FormDescription className="hidden sm:block">
                    Who will receive this order
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="storeCodeId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">Store Code</FormLabel>
                  <FormControl>
                    <Combobox
                      options={storeCodeOptions}
                      value={field.value || ""}
                      onValueChange={field.onChange}
                      placeholder="Select store code..."
                      searchPlaceholder="Search store codes..."
                      emptyText="No store code found."
                      onCreateNew={handleCreateStoreCode}
                      isCreating={isCreatingStoreCode}
                      className="h-9 text-base"
                    />
                  </FormControl>
                  <FormDescription className="hidden sm:block">
                    Where this order will be purchased
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />


          </div>
        </Card>

        {/* Image Upload */}
        <Card className="p-3">
          <Label className="text-base font-medium">Product Image</Label>
          <div className="mt-2 flex justify-center">
            {imagePreview ? (
              <div className="flex justify-center">
                <ImagePreview
                  src={imagePreview}
                  alt="Product preview"
                  showChangeButton={true}
                  onChangeImage={triggerImageUpload}
                  onRemoveImage={removeImage}
                  isEditing={true}
                  useToggleMode={true}
                  initialSize="full"
                />
              </div>
            ) : (
              <div className="border-2 border-dashed border-border rounded-lg p-4 text-center hover:border-border/80 transition-colors w-full max-w-sm">
                <LuUpload className="mx-auto h-8 w-8 text-muted-foreground" />
                <div className="mt-2 flex justify-center">
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={triggerImageUpload}
                    className="h-7 px-3 text-primary hover:text-primary/80 text-sm font-medium"
                    aria-label="Upload product image"
                  >
                    <span className="hidden xs:inline">Upload an Image</span>
                    <span className="xs:hidden">Upload</span>
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-1">PNG, JPG up to 10MB</p>
              </div>
            )}

            {/* Hidden file input */}
            <Input
              id="image-upload"
              type="file"
              accept="image/*"
              className="hidden"
              onChange={handleImageChange}
              aria-label="Select image file"
            />
          </div>
        </Card>

        {/* Basic Information */}
        <Card className="p-3">
          <h3 className="text-base font-medium mb-3">Basic Information</h3>
          <div className="space-y-3">
            <FormField
              control={form.control}
              name="productName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">Product Name *</FormLabel>
                  <FormControl>
                    <Combobox
                      options={productNameOptions}
                      value={field.value || ""}
                      onValueChange={field.onChange}
                      placeholder="Product name..."
                      searchPlaceholder="Search product names..."
                      emptyText="No matching product names found."
                      mode="autocomplete"
                      className="h-9 text-base"
                    />
                  </FormControl>
                  <FormDescription className="hidden sm:block">
                    Start typing to see suggestions
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Quantity and Usage Unit - Horizontal Layout */}
            <div className="grid grid-cols-2 gap-3">
              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Quantity *</FormLabel>
                    <FormControl>
                      {/* Mobile-optimized quantity controls with compact design */}
                      <div className="flex items-center gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          className="w-9 h-7 px-2 flex-shrink-0"
                          onClick={() => adjustQuantity(-1)}
                          disabled={field.value <= 1}
                          title="Decrease quantity"
                          aria-label="Decrease quantity"
                        >
                          <LuMinus className="h-3 w-3" />
                        </Button>
                        <Input
                          type="number"
                          min="1"
                          max="9999"
                          inputMode="numeric"
                          pattern="[0-9]*"
                          className="text-center flex-1 h-9 text-base font-medium"
                          placeholder="1"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                          aria-label="Item quantity"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          className="w-9 h-7 px-2 flex-shrink-0"
                          onClick={() => adjustQuantity(1)}
                          title="Increase quantity"
                          aria-label="Increase quantity"
                        >
                          <LuPlus className="h-3 w-3" />
                        </Button>
                      </div>
                    </FormControl>
                    <FormDescription className="hidden sm:block">
                      Number of orders
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="usageUnit"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Usage Unit</FormLabel>
                    <FormControl>
                      <Combobox
                        options={usageUnitOptions}
                        value={field.value || ""}
                        onValueChange={field.onChange}
                        placeholder="Usage unit..."
                        searchPlaceholder="Search usage units..."
                        emptyText="No usage unit found."
                        onCreateNew={handleCreateUsageUnit}
                        isCreating={isCreatingUsageUnit}
                        className="h-9 text-base"
                      />
                    </FormControl>
                    <FormDescription className="hidden sm:block">
                      e.g., kg, pcs, liters
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="comment"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">Notes</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Notes..."
                      className="h-9 text-base"
                      maxLength={500}
                      autoComplete="off"
                      {...field}
                      aria-label="Additional comments about this item"
                    />
                  </FormControl>
                  <FormDescription className="hidden sm:block">
                    Optional notes (max 500 chars)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </Card>

        {/* Pricing */}
        <Card className="p-3">
          <h3 className="text-base font-medium mb-3">Pricing</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            <FormField
              control={form.control}
              name="storePrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">Store Price *</FormLabel>
                  <FormControl>
                    {/* Compact price controls */}
                    <div className="flex items-center gap-2">
                      <div className="flex-1 relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground text-sm">₱</span>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          max="999999.99"
                          inputMode="decimal"
                          className="text-center flex-1 h-9 text-base font-medium pl-7"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          aria-label="Store price in pesos"
                        />
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        className="w-9 h-7 px-2 flex-shrink-0"
                        onClick={() => adjustPrice('storePrice', 1)}
                        title="Increase store price"
                        aria-label="Increase store price"
                      >
                        <LuPlus className="h-3 w-3" />
                      </Button>
                    </div>
                  </FormControl>
                  <FormDescription className="hidden sm:block">
                    Price per unit at store
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pasabuyFee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">Pasabuy Fee *</FormLabel>
                  <FormControl>
                    {/* Compact price controls */}
                    <div className="flex items-center gap-2">
                      <div className="flex-1 relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground text-sm">₱</span>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          max="999999.99"
                          inputMode="decimal"
                          className="text-center flex-1 h-9 text-base font-medium pl-7"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          aria-label="Pasabuy fee in pesos"
                        />
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        className="w-9 h-7 px-2 flex-shrink-0"
                        onClick={() => adjustPrice('pasabuyFee', 1)}
                        title="Increase pasabuy fee"
                        aria-label="Increase pasabuy fee"
                      >
                        <LuPlus className="h-3 w-3" />
                      </Button>
                    </div>
                  </FormControl>
                  <FormDescription className="hidden sm:block">
                    Service fee per unit
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="customerPrice"
              render={({ field }) => (
                <FormItem className="sm:col-span-2 lg:col-span-1">
                  <FormLabel className="text-sm font-medium">Customer Price *</FormLabel>
                  <FormControl>
                    {/* Compact price controls */}
                    <div className="flex items-center gap-2">
                      <div className="flex-1 relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground text-sm">₱</span>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          max="999999.99"
                          inputMode="decimal"
                          className="text-center flex-1 h-9 text-base font-medium pl-7"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          aria-label="Customer price in pesos"
                        />
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        className="w-9 h-7 px-2 flex-shrink-0"
                        onClick={() => adjustPrice('customerPrice', 1)}
                        title="Increase customer price"
                        aria-label="Increase customer price"
                      >
                        <LuPlus className="h-3 w-3" />
                      </Button>
                    </div>
                  </FormControl>
                  <FormDescription className="hidden sm:block">
                    Price charged to customer
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Calculations Display */}
          <div className="mt-4 p-3 bg-muted/50 rounded-lg border">
            <h4 className="font-medium mb-2 text-sm">Price Calculations</h4>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 text-sm">
              <div className="text-center sm:text-left">
                <span className="text-muted-foreground block">Order Profit:</span>
                <p className="font-semibold text-base text-green-600">₱{orderProfit.toFixed(2)}</p>
              </div>
              <div className="text-center sm:text-left">
                <span className="text-muted-foreground block">Total Store Cost:</span>
                <p className="font-semibold text-base">₱{orderTotalStoreCost.toFixed(2)}</p>
              </div>
              <div className="text-center sm:text-left">
                <span className="text-muted-foreground block">Total Customer Price:</span>
                <p className="font-semibold text-base text-blue-600">₱{orderTotalCustomerPrice.toFixed(2)}</p>
              </div>
            </div>
          </div>
        </Card>

        {/* Pricing Calculator */}
        <PricingCalculator
          storePrice={storePrice}
          storeCodeId={form.watch('storeCodeId') ? parseInt(form.watch('storeCodeId')) : undefined}
          onPriceCalculated={(result) => {
            // Auto-fill pasabuy fee and customer price with calculated values
            form.setValue('pasabuyFee', result.pasabuyFee)
            form.setValue('customerPrice', result.customerPrice)
          }}
          className="border-blue-200 bg-blue-50/50"
        />

        {/* Form Submission Feedback */}
        {submitError && (
          <Card className="p-3 bg-red-50 border-red-200">
            <div className="flex items-start gap-2">
              <LuTriangleAlert className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-red-800 text-sm">Error Saving Order</h4>
                <p className="text-red-700 text-sm mt-1">{submitError}</p>
              </div>
            </div>
          </Card>
        )}

        {submitSuccess && (
          <Card className="p-3 bg-green-50 border-green-200">
            <div className="flex items-start gap-2">
              <LuLoader className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-green-800 text-sm">Order Saved Successfully</h4>
                <p className="text-green-700 text-sm mt-1">Your order has been saved and is ready for processing.</p>
              </div>
            </div>
          </Card>
        )}

        {/* Form Actions - Compact design with maintained functionality */}
        <div className="flex flex-col gap-3">
          {/* Delete button for editing mode */}
          {isEditing && onDelete && orderId && (
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteClick}
              className="w-full h-9 text-sm font-medium"
              title="Delete this order permanently"
              aria-label="Delete this order permanently"
            >
              <LuTrash2 className="h-4 w-4 mr-2" />
              Delete Order
            </Button>
          )}

          {/* Main action buttons */}
          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting || isLoading}
              className="flex-1 h-9 text-sm font-medium order-2 sm:order-1"
              aria-label="Cancel and go back"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || isLoading}
              className="flex-1 h-9 text-sm font-medium order-1 sm:order-2 relative"
              aria-label={isEditing ? "Save changes to order" : "Create new order"}
            >
              {(isSubmitting || isLoading) && (
                <LuLoader className="h-4 w-4 mr-2 animate-spin" />
              )}
              {isSubmitting || isLoading ? 'Saving...' : isEditing ? 'Save Changes' : 'Create Order'}
            </Button>
          </div>
        </div>
      </form>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.isOpen} onOpenChange={(open) => !open && handleDeleteCancel()}>
        <DialogContent className="sm:max-w-md mx-4">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-base">
              <LuTriangleAlert className="h-4 w-4 text-destructive flex-shrink-0" />
              Delete Order
            </DialogTitle>
            <DialogDescription className="space-y-2 text-sm">
              <div>Are you sure you want to delete this order? This action cannot be undone.</div>
              {deleteDialog.hasInvoiceItems && (
                <div className="p-2 bg-red-50 border border-red-200 rounded-md">
                  <div className="flex items-start gap-2">
                    <LuTriangleAlert className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                    <div className="text-red-800 text-xs">
                      <strong>Warning:</strong> This order is included in one or more invoices. Deleting it will also remove it from those invoices.
                    </div>
                  </div>
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleDeleteCancel}
              disabled={deleteDialog.isLoading}
              className="h-9 text-sm font-medium order-2 sm:order-1"
              aria-label="Cancel deletion"
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={deleteDialog.isLoading}
              className="h-9 text-sm font-medium order-1 sm:order-2"
              aria-label="Confirm deletion"
            >
              {deleteDialog.isLoading && (
                <LuLoader className="h-4 w-4 mr-2 animate-spin" />
              )}
              {deleteDialog.isLoading ? 'Deleting...' : 'Delete Order'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Form>
  )
}
